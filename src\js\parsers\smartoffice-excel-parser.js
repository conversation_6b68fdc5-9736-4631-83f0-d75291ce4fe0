/**
 * SmartOffice Excel解析器
 * 纯JavaScript实现的XLSX文件解析器，支持基本的Excel文件读取
 * @function ExcelParser - Excel文件解析和数据提取
 */

(function() {
    'use strict';

    /**
     * Excel解析器构造函数
     * @function ExcelParser - 创建Excel解析器实例
     */
    function ExcelParser() {
        this.eventBus = SmartOffice.Core.EventBus;
        this.dataValidator = new SmartOffice.Data.DataValidator();
        
        // 支持的文件类型
        this.supportedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        console.log('📊 Excel解析器: 初始化完成');
    }

    /**
     * 检查文件是否为支持的Excel格式
     * @function isExcelFile - 验证文件格式
     * @param {File} file - 要检查的文件对象
     * @returns {boolean} 是否为支持的Excel文件
     */
    ExcelParser.prototype.isExcelFile = function(file) {
        if (!file) {
            return false;
        }
        
        // 检查MIME类型
        if (this.supportedTypes.includes(file.type)) {
            return true;
        }
        
        // 检查文件扩展名
        const fileName = file.name.toLowerCase();
        return fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
    };

    /**
     * 解析Excel文件内容（与CSV解析器兼容的接口）
     * @function parse - 解析Excel文件内容
     * @param {ArrayBuffer} content - 文件内容（ArrayBuffer格式）
     * @param {Object} options - 解析选项
     * @param {Function} options.onProgress - 进度回调
     * @param {Function} options.onComplete - 完成回调
     * @param {Function} options.onError - 错误回调
     */
    ExcelParser.prototype.parse = function(content, options) {
        const self = this;
        options = options || {};

        try {
            console.log('📊 Excel解析器: 开始解析内容');

            // 模拟进度更新
            if (options.onProgress) {
                options.onProgress(0.1);
            }

            // 使用setTimeout模拟异步处理，避免阻塞UI
            setTimeout(function() {
                try {
                    if (options.onProgress) {
                        options.onProgress(0.3);
                    }

                    const workbook = self.parseWorkbook(content);

                    if (options.onProgress) {
                        options.onProgress(0.6);
                    }

                    // 获取第一个工作表的数据
                    const worksheet = self.getFirstWorksheet(workbook);
                    const data = self.extractData(worksheet);

                    if (options.onProgress) {
                        options.onProgress(0.8);
                    }

                    // 验证和标准化数据
                    const result = self.processData(data, 'excel_file.xlsx');

                    if (options.onProgress) {
                        options.onProgress(1.0);
                    }

                    console.log('✅ Excel解析器: 解析完成，共', result.data.length, '行数据');

                    if (options.onComplete) {
                        options.onComplete(result);
                    }

                } catch (error) {
                    console.error('❌ Excel解析器: 解析失败', error);
                    if (options.onError) {
                        options.onError(error);
                    }
                }
            }, 100);

        } catch (error) {
            console.error('❌ Excel解析器: 初始化失败', error);
            if (options.onError) {
                options.onError(error);
            }
        }
    };

    /**
     * 解析Excel文件
     * @function parseFile - 解析Excel文件并提取数据
     * @param {File} file - Excel文件对象
     * @returns {Promise} 解析结果Promise
     */
    ExcelParser.prototype.parseFile = function(file) {
        return new Promise(function(resolve, reject) {
            if (!this.isExcelFile(file)) {
                reject(new Error('不支持的文件格式，请选择.xlsx或.xls文件'));
                return;
            }

            console.log('📊 Excel解析器: 开始解析文件', file.name);
            this.eventBus.emit('excel:parse:start', { file: file });

            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const workbook = this.parseWorkbook(arrayBuffer);

                    // 获取第一个工作表的数据
                    const worksheet = this.getFirstWorksheet(workbook);
                    const data = this.extractData(worksheet);

                    // 验证和标准化数据
                    const result = this.processData(data, file.name);

                    console.log('✅ Excel解析器: 解析完成，共', result.data.length, '行数据');
                    this.eventBus.emit('excel:parse:complete', result);

                    resolve(result);
                } catch (error) {
                    console.error('❌ Excel解析器: 解析失败', error);
                    this.eventBus.emit('excel:parse:error', { error: error });
                    reject(error);
                }
            }.bind(this);

            reader.onerror = function() {
                const error = new Error('文件读取失败');
                console.error('❌ Excel解析器: 文件读取失败');
                this.eventBus.emit('excel:parse:error', { error: error });
                reject(error);
            }.bind(this);

            reader.readAsArrayBuffer(file);
        }.bind(this));
    };

    /**
     * 解析工作簿结构（简化版ZIP解析）
     * @function parseWorkbook - 解析Excel工作簿结构
     * @param {ArrayBuffer} arrayBuffer - 文件数据
     * @returns {Object} 工作簿对象
     */
    ExcelParser.prototype.parseWorkbook = function(arrayBuffer) {
        // 这是一个简化的实现，实际的XLSX解析需要完整的ZIP解析
        // 为了保持零依赖，这里实现一个基础版本
        
        const uint8Array = new Uint8Array(arrayBuffer);
        
        // 检查ZIP文件头
        if (uint8Array[0] !== 0x50 || uint8Array[1] !== 0x4B) {
            throw new Error('无效的Excel文件格式');
        }
        
        // 简化的工作簿对象
        return {
            arrayBuffer: arrayBuffer,
            uint8Array: uint8Array,
            worksheets: ['Sheet1'] // 简化版本只支持第一个工作表
        };
    };

    /**
     * 获取第一个工作表
     * @function getFirstWorksheet - 获取第一个工作表数据
     * @param {Object} workbook - 工作簿对象
     * @returns {Object} 工作表对象
     */
    ExcelParser.prototype.getFirstWorksheet = function(workbook) {
        // 简化实现：尝试从ZIP中提取工作表数据
        // 实际实现需要解析XML结构
        
        return {
            name: 'Sheet1',
            data: this.extractSimpleData(workbook.uint8Array)
        };
    };

    /**
     * 从工作表提取数据
     * @function extractData - 从工作表提取结构化数据
     * @param {Object} worksheet - 工作表对象
     * @returns {Array} 数据数组
     */
    ExcelParser.prototype.extractData = function(worksheet) {
        return worksheet.data || [];
    };

    /**
     * 处理和标准化数据
     * @function processData - 处理解析后的数据
     * @param {Array} rawData - 原始数据数组
     * @param {string} fileName - 文件名
     * @returns {Object} 处理后的数据对象
     */
    ExcelParser.prototype.processData = function(rawData, fileName) {
        if (!rawData || rawData.length === 0) {
            throw new Error('Excel文件中没有找到数据');
        }

        // 第一行作为表头
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // 验证表头
        if (!headers || headers.length === 0) {
            throw new Error('Excel文件中没有找到表头');
        }

        // 转换为对象数组
        const data = dataRows.map(function(row, index) {
            const rowData = {};
            headers.forEach(function(header, colIndex) {
                const value = row[colIndex];
                rowData[header] = value !== undefined ? String(value).trim() : '';
            });
            return rowData;
        });

        // 过滤空行
        const filteredData = data.filter(function(row) {
            return Object.values(row).some(function(value) {
                return value && value.trim() !== '';
            });
        });

        // 构建解析结果对象
        const parseResult = {
            fileName: fileName,
            fileType: 'excel',
            headers: headers,
            data: filteredData,
            rowCount: filteredData.length,
            columnCount: headers.length,
            statistics: this.generateStatistics(filteredData, headers),
            parseTime: new Date().toISOString()
        };

        // 数据验证和类型检测 - 使用正确的方法名
        const validationResult = this.dataValidator.validateParsedData(parseResult);

        // 合并验证结果
        parseResult.isValid = validationResult.isValid;
        parseResult.errors = validationResult.errors || [];
        parseResult.warnings = validationResult.warnings || [];
        parseResult.validationMessage = validationResult.message || '';

        return parseResult;
    };

    /**
     * 生成数据统计信息
     * @function generateStatistics - 生成数据统计
     * @param {Array} data - 数据数组
     * @param {Array} headers - 表头数组
     * @returns {Object} 统计信息对象
     */
    ExcelParser.prototype.generateStatistics = function(data, headers) {
        const stats = {};
        
        headers.forEach(function(header) {
            const values = data.map(function(row) {
                return row[header];
            }).filter(function(value) {
                return value && value.trim() !== '';
            });
            
            stats[header] = {
                totalCount: data.length,
                validCount: values.length,
                emptyCount: data.length - values.length,
                uniqueCount: new Set(values).size,
                sampleValues: values.slice(0, 5)
            };
        });
        
        return stats;
    };

    /**
     * 获取支持的文件类型
     * @function getSupportedTypes - 获取支持的文件类型列表
     * @returns {Array} 支持的MIME类型数组
     */
    ExcelParser.prototype.getSupportedTypes = function() {
        return this.supportedTypes.slice();
    };

    /**
     * 从Excel二进制数据中提取实际数据
     * @function extractSimpleData - 解析XLSX文件并提取工作表数据
     * @param {Uint8Array} uint8Array - Excel文件的二进制数据
     * @returns {Array} 提取的数据数组（二维数组格式）
     */
    ExcelParser.prototype.extractSimpleData = function(uint8Array) {
        console.log('📊 Excel解析器: 开始解析XLSX文件内容');

        try {
            // 1. 解析ZIP文件结构
            const zipEntries = this.parseZipFile(uint8Array);
            console.log('📦 找到', zipEntries.length, '个ZIP条目');

            // 2. 查找工作表XML文件
            const worksheetEntry = this.findWorksheetEntry(zipEntries);
            if (!worksheetEntry) {
                throw new Error('未找到工作表数据，可能不是有效的Excel文件');
            }

            // 3. 提取工作表XML内容
            const worksheetXML = this.extractFileContent(uint8Array, worksheetEntry);
            console.log('📄 工作表XML长度:', worksheetXML.length);

            // 4. 查找并解析共享字符串（如果存在）
            const sharedStrings = this.extractSharedStrings(uint8Array, zipEntries);

            // 5. 解析工作表数据
            const data = this.parseWorksheetData(worksheetXML, sharedStrings);

            console.log('✅ Excel解析完成，提取到', data.length, '行数据');
            return data;

        } catch (error) {
            console.error('❌ Excel解析失败:', error.message);
            // 如果解析失败，返回错误信息而不是演示数据
            throw new Error('Excel文件解析失败: ' + error.message);
        }
    };

    /**
     * 解析ZIP文件结构
     * @function parseZipFile - 解析ZIP文件的中央目录
     * @param {Uint8Array} uint8Array - ZIP文件的二进制数据
     * @returns {Array} ZIP条目数组
     */
    ExcelParser.prototype.parseZipFile = function(uint8Array) {
        // 查找中央目录结束记录
        const eocdOffset = this.findEndOfCentralDirectory(uint8Array);
        if (eocdOffset === -1) {
            throw new Error('无效的ZIP文件格式');
        }

        // 读取中央目录信息
        const view = new DataView(uint8Array.buffer);
        const centralDirOffset = view.getUint32(eocdOffset + 16, true);
        const entryCount = view.getUint16(eocdOffset + 10, true);

        // 解析中央目录条目
        const entries = [];
        let offset = centralDirOffset;

        for (let i = 0; i < entryCount; i++) {
            const entry = this.parseCentralDirectoryEntry(uint8Array, offset);
            entries.push(entry);
            offset += entry.totalSize;
        }

        return entries;
    };

    /**
     * 查找ZIP文件的中央目录结束记录
     * @function findEndOfCentralDirectory - 查找EOCD记录
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @returns {number} EOCD记录的偏移位置，-1表示未找到
     */
    ExcelParser.prototype.findEndOfCentralDirectory = function(uint8Array) {
        // EOCD签名: 0x06054b50
        const signature = 0x06054b50;
        const view = new DataView(uint8Array.buffer);

        // 从文件末尾向前搜索EOCD记录
        for (let i = uint8Array.length - 22; i >= 0; i--) {
            if (view.getUint32(i, true) === signature) {
                return i;
            }
        }

        return -1;
    };

    /**
     * 解析中央目录条目
     * @function parseCentralDirectoryEntry - 解析单个中央目录条目
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {number} offset - 条目起始偏移
     * @returns {Object} 解析后的条目信息
     */
    ExcelParser.prototype.parseCentralDirectoryEntry = function(uint8Array, offset) {
        const view = new DataView(uint8Array.buffer);

        // 读取文件名长度
        const fileNameLength = view.getUint16(offset + 28, true);
        const extraFieldLength = view.getUint16(offset + 30, true);
        const commentLength = view.getUint16(offset + 32, true);

        // 读取文件名
        const fileNameBytes = uint8Array.slice(offset + 46, offset + 46 + fileNameLength);
        const fileName = new TextDecoder('utf-8').decode(fileNameBytes);

        // 读取其他重要信息
        const compressedSize = view.getUint32(offset + 20, true);
        const uncompressedSize = view.getUint32(offset + 24, true);
        const localHeaderOffset = view.getUint32(offset + 42, true);
        const compressionMethod = view.getUint16(offset + 10, true);

        return {
            fileName: fileName,
            compressedSize: compressedSize,
            uncompressedSize: uncompressedSize,
            localHeaderOffset: localHeaderOffset,
            compressionMethod: compressionMethod,
            totalSize: 46 + fileNameLength + extraFieldLength + commentLength
        };
    };

    /**
     * 查找工作表XML文件条目
     * @function findWorksheetEntry - 在ZIP条目中查找工作表文件
     * @param {Array} zipEntries - ZIP文件条目数组
     * @returns {Object|null} 工作表条目对象，未找到返回null
     */
    ExcelParser.prototype.findWorksheetEntry = function(zipEntries) {
        // 查找第一个工作表文件
        for (let i = 0; i < zipEntries.length; i++) {
            const entry = zipEntries[i];
            if (entry.fileName === 'xl/worksheets/sheet1.xml') {
                return entry;
            }
        }

        // 如果没找到sheet1.xml，查找其他工作表文件
        for (let i = 0; i < zipEntries.length; i++) {
            const entry = zipEntries[i];
            if (entry.fileName.startsWith('xl/worksheets/') && entry.fileName.endsWith('.xml')) {
                return entry;
            }
        }

        return null;
    };

    /**
     * 提取ZIP文件中的文件内容
     * @function extractFileContent - 提取指定文件的内容
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {Object} entry - 文件条目信息
     * @returns {string} 文件内容（文本格式）
     */
    ExcelParser.prototype.extractFileContent = function(uint8Array, entry) {
        const view = new DataView(uint8Array.buffer);

        // 读取本地文件头
        const localHeaderOffset = entry.localHeaderOffset;
        const fileNameLength = view.getUint16(localHeaderOffset + 26, true);
        const extraFieldLength = view.getUint16(localHeaderOffset + 28, true);

        // 计算文件数据的起始位置
        const dataOffset = localHeaderOffset + 30 + fileNameLength + extraFieldLength;

        // 提取文件数据
        const fileData = uint8Array.slice(dataOffset, dataOffset + entry.compressedSize);

        // 如果文件未压缩，直接转换为文本
        if (entry.compressionMethod === 0) {
            return new TextDecoder('utf-8').decode(fileData);
        }

        // 简化处理：对于压缩文件，尝试直接解码
        // 实际实现中需要DEFLATE解压缩
        try {
            return new TextDecoder('utf-8').decode(fileData);
        } catch (error) {
            throw new Error('无法解压缩文件内容，可能需要DEFLATE解压缩支持');
        }
    };

    /**
     * 提取共享字符串表
     * @function extractSharedStrings - 提取并解析共享字符串
     * @param {Uint8Array} uint8Array - ZIP文件数据
     * @param {Array} zipEntries - ZIP文件条目数组
     * @returns {Array} 共享字符串数组
     */
    ExcelParser.prototype.extractSharedStrings = function(uint8Array, zipEntries) {
        // 查找共享字符串文件
        const sharedStringsEntry = zipEntries.find(function(entry) {
            return entry.fileName === 'xl/sharedStrings.xml';
        });

        if (!sharedStringsEntry) {
            console.log('📄 未找到共享字符串文件，使用直接值');
            return [];
        }

        try {
            const xmlContent = this.extractFileContent(uint8Array, sharedStringsEntry);
            return this.parseSharedStringsXML(xmlContent);
        } catch (error) {
            console.warn('⚠️ 共享字符串解析失败:', error.message);
            return [];
        }
    };

    /**
     * 解析共享字符串XML
     * @function parseSharedStringsXML - 解析共享字符串XML内容
     * @param {string} xmlContent - XML内容
     * @returns {Array} 字符串数组
     */
    ExcelParser.prototype.parseSharedStringsXML = function(xmlContent) {
        const strings = [];

        // 使用正则表达式提取<t>标签中的文本
        const regex = /<t[^>]*>(.*?)<\/t>/g;
        let match;

        while ((match = regex.exec(xmlContent)) !== null) {
            strings.push(match[1]);
        }

        return strings;
    };

    /**
     * 解析工作表数据
     * @function parseWorksheetData - 解析工作表XML并提取数据
     * @param {string} xmlContent - 工作表XML内容
     * @param {Array} sharedStrings - 共享字符串数组
     * @returns {Array} 二维数据数组
     */
    ExcelParser.prototype.parseWorksheetData = function(xmlContent, sharedStrings) {
        const cellData = {};

        // 提取所有单元格数据
        const cellRegex = /<c r="([A-Z]+\d+)"[^>]*(?:\s+t="([^"]*)")?[^>]*>(?:<v>(.*?)<\/v>)?/g;
        let match;

        while ((match = cellRegex.exec(xmlContent)) !== null) {
            const cellRef = match[1];  // 如 "A1", "B2"
            const cellType = match[2]; // 单元格类型
            const cellValue = match[3]; // 单元格值

            if (cellValue !== undefined) {
                let value = cellValue;

                // 如果是共享字符串类型，从共享字符串表中获取实际值
                if (cellType === 's' && sharedStrings.length > 0) {
                    const index = parseInt(cellValue, 10);
                    value = sharedStrings[index] || '';
                }

                cellData[cellRef] = value;
            }
        }

        // 将单元格数据转换为二维数组
        return this.convertCellDataToArray(cellData);
    };

    /**
     * 将单元格数据转换为二维数组
     * @function convertCellDataToArray - 将单元格引用数据转换为二维数组
     * @param {Object} cellData - 单元格数据对象，键为单元格引用（如A1），值为单元格值
     * @returns {Array} 二维数据数组
     */
    ExcelParser.prototype.convertCellDataToArray = function(cellData) {
        if (Object.keys(cellData).length === 0) {
            throw new Error('工作表中没有找到数据');
        }

        // 解析所有单元格引用，找出数据范围
        let maxRow = 0;
        let maxCol = 0;
        const cellRefs = Object.keys(cellData);

        cellRefs.forEach(function(cellRef) {
            const parsed = this.parseCellReference(cellRef);
            maxRow = Math.max(maxRow, parsed.row);
            maxCol = Math.max(maxCol, parsed.col);
        }.bind(this));

        // 创建二维数组
        const data = [];
        for (let row = 0; row < maxRow; row++) {
            data[row] = [];
            for (let col = 0; col < maxCol; col++) {
                const cellRef = this.getCellReference(row + 1, col + 1);
                data[row][col] = cellData[cellRef] || '';
            }
        }

        return data;
    };

    /**
     * 解析单元格引用
     * @function parseCellReference - 解析单元格引用（如A1）为行列数字
     * @param {string} cellRef - 单元格引用（如"A1", "B2"）
     * @returns {Object} 包含行列信息的对象
     */
    ExcelParser.prototype.parseCellReference = function(cellRef) {
        const match = cellRef.match(/^([A-Z]+)(\d+)$/);
        if (!match) {
            throw new Error('无效的单元格引用: ' + cellRef);
        }

        const colStr = match[1];
        const rowStr = match[2];

        // 将列字母转换为数字（A=1, B=2, ..., Z=26, AA=27, ...）
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
            col = col * 26 + (colStr.charCodeAt(i) - 64);
        }

        const row = parseInt(rowStr, 10);

        return { row: row, col: col };
    };

    /**
     * 生成单元格引用
     * @function getCellReference - 根据行列数字生成单元格引用
     * @param {number} row - 行号（从1开始）
     * @param {number} col - 列号（从1开始）
     * @returns {string} 单元格引用（如"A1"）
     */
    ExcelParser.prototype.getCellReference = function(row, col) {
        let colStr = '';
        let tempCol = col;

        while (tempCol > 0) {
            tempCol--;
            colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
            tempCol = Math.floor(tempCol / 26);
        }

        return colStr + row;
    };

    // 注册到全局命名空间
    if (!SmartOffice.Parsers) {
        SmartOffice.Parsers = {};
    }
    SmartOffice.Parsers.ExcelParser = ExcelParser;

    console.log('📊 Excel解析器: 模块加载完成');

})();
