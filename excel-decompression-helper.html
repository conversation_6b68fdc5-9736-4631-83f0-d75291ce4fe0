<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel解压缩助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .helper-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .solution-card {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background-color: #f8f9fa;
        }
        .solution-card h3 {
            margin-top: 0;
            color: #007AFF;
        }
        .code-block {
            background-color: #f1f3f4;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 12px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px 4px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="helper-container">
        <h1>📊 Excel解压缩问题解决方案</h1>
        <p>您的Excel文件使用了DEFLATE压缩，我们的解析器无法直接处理。以下是几种解决方案：</p>

        <div class="solution-card">
            <h3>🚀 方案一：添加Pako解压缩库（推荐）</h3>
            <p>Pako是一个轻量级的JavaScript压缩库，可以处理DEFLATE压缩。</p>
            
            <h4>步骤1：在HTML中添加Pako库</h4>
            <div class="code-block">
&lt;!-- 在index.html的&lt;head&gt;部分添加 --&gt;
&lt;script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"&gt;&lt;/script&gt;
            </div>

            <h4>步骤2：测试Pako库是否加载</h4>
            <button class="btn" onclick="testPako()">测试Pako库</button>
            <div id="pakoResult"></div>

            <div class="success">
                <strong>优点：</strong> 完全支持DEFLATE压缩，解压缩效果最好，库很小（约45KB）
            </div>
        </div>

        <div class="solution-card">
            <h3>🔧 方案二：使用浏览器原生API</h3>
            <p>现代浏览器支持DecompressionStream API，但兼容性有限。</p>
            
            <button class="btn" onclick="testNativeAPI()">测试原生API支持</button>
            <div id="nativeResult"></div>

            <div class="warning">
                <strong>注意：</strong> 此API在较老的浏览器中不可用，需要Chrome 80+或Firefox 65+
            </div>
        </div>

        <div class="solution-card">
            <h3>📁 方案三：使用未压缩的Excel文件</h3>
            <p>将Excel文件保存为未压缩格式，避免解压缩问题。</p>
            
            <h4>操作步骤：</h4>
            <ol>
                <li>在Excel中打开您的文件</li>
                <li>选择"文件" → "另存为"</li>
                <li>选择"Excel工作簿(*.xlsx)"格式</li>
                <li>在保存选项中选择"最小压缩"或"无压缩"</li>
                <li>保存并重新上传</li>
            </ol>

            <div class="warning">
                <strong>缺点：</strong> 文件大小会增加，但兼容性最好
            </div>
        </div>

        <div class="solution-card">
            <h3>🔄 方案四：转换为CSV格式</h3>
            <p>将Excel文件转换为CSV格式，我们的CSV解析器工作完美。</p>
            
            <h4>操作步骤：</h4>
            <ol>
                <li>在Excel中打开您的文件</li>
                <li>选择"文件" → "另存为"</li>
                <li>选择"CSV UTF-8(逗号分隔)(*.csv)"格式</li>
                <li>保存并上传CSV文件</li>
            </ol>

            <div class="success">
                <strong>优点：</strong> 100%兼容，文件小，解析快速
            </div>
        </div>

        <div class="helper-container">
            <h2>🛠️ 立即修复</h2>
            <p>如果您选择方案一（推荐），点击下面的按钮自动添加Pako库支持：</p>
            
            <button class="btn" onclick="addPakoSupport()">自动添加Pako库支持</button>
            <button class="btn btn-secondary" onclick="downloadPakoScript()">下载Pako库文件</button>
            
            <div id="fixResult"></div>
        </div>
    </div>

    <script>
        function testPako() {
            const resultDiv = document.getElementById('pakoResult');
            if (typeof pako !== 'undefined') {
                resultDiv.innerHTML = '<div class="success">✅ Pako库已加载，可以处理DEFLATE压缩！</div>';
            } else {
                resultDiv.innerHTML = '<div class="warning">❌ Pako库未加载，请先添加库文件。</div>';
            }
        }

        function testNativeAPI() {
            const resultDiv = document.getElementById('nativeResult');
            if (typeof DecompressionStream !== 'undefined') {
                resultDiv.innerHTML = '<div class="success">✅ 浏览器支持原生解压缩API！</div>';
            } else {
                resultDiv.innerHTML = '<div class="warning">❌ 浏览器不支持原生解压缩API，建议使用Pako库。</div>';
            }
        }

        function addPakoSupport() {
            const resultDiv = document.getElementById('fixResult');
            
            // 检查是否已经加载
            if (typeof pako !== 'undefined') {
                resultDiv.innerHTML = '<div class="success">✅ Pako库已经加载，无需重复添加！</div>';
                return;
            }

            // 动态加载Pako库
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js';
            script.onload = function() {
                resultDiv.innerHTML = '<div class="success">✅ Pako库加载成功！现在可以处理压缩的Excel文件了。<br>请刷新页面并重新上传Excel文件。</div>';
            };
            script.onerror = function() {
                resultDiv.innerHTML = '<div class="warning">❌ Pako库加载失败，请检查网络连接或手动下载。</div>';
            };
            
            document.head.appendChild(script);
            resultDiv.innerHTML = '<div class="warning">⏳ 正在加载Pako库...</div>';
        }

        function downloadPakoScript() {
            const resultDiv = document.getElementById('fixResult');
            resultDiv.innerHTML = `
                <div class="success">
                    📥 请下载Pako库文件：<br>
                    <a href="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js" target="_blank">下载pako.min.js</a><br>
                    下载后将文件放在项目目录中，并在index.html中添加：<br>
                    <code>&lt;script src="pako.min.js"&gt;&lt;/script&gt;</code>
                </div>
            `;
        }

        // 页面加载时自动检测
        window.addEventListener('load', function() {
            testPako();
            testNativeAPI();
        });
    </script>
</body>
</html>
